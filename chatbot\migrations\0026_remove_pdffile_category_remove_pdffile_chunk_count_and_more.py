# Generated by Django 5.2.2 on 2025-09-02 03:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0025_add_document_category'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='pdffile',
            name='category',
        ),
        migrations.RemoveField(
            model_name='pdffile',
            name='chunk_count',
        ),
        migrations.RemoveField(
            model_name='pdffile',
            name='file_hash',
        ),
        migrations.RemoveField(
            model_name='pdffile',
            name='is_processed',
        ),
        migrations.RemoveField(
            model_name='pdffile',
            name='weaviate_class',
        ),
        migrations.AddField(
            model_name='pdffile',
            name='camera_type',
            field=models.CharField(blank=True, choices=[('area_scan', 'Area Scan'), ('line_scan', 'Line Scan')], max_length=20, null=True),
        ),
    ]
