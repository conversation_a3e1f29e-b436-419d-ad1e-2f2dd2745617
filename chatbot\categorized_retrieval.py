"""
Categorized Weaviate Retrieval System

This module provides category-aware retrieval from Weaviate classes based on
product hierarchy and ticket information.
"""

import weaviate
from typing import List, Dict, Optional
from .models import SupportTicket

# Configuration
WEAVIATE_URL = "http://localhost:8080"

# Category mapping based on product hierarchy
PRODUCT_CATEGORY_MAPPING = {
    # Camera categories
    "Area Scan": "AreaScan",
    "Line Scan": "LineScan",
    
    # Frame Grabber categories  
    "Frame Grabber": "FrameGrabber",
    
    # Software categories
    "Software": "Software",
    "Sapera LT": "Software",
    "Spinnaker SDK": "Software",
    
    # Default fallback
    "default": "GeneralChunks"
}

# Weaviate class priorities for multi-class search
CLASS_SEARCH_PRIORITY = [
    "AreaScan",
    "LineScan", 
    "FrameGrabber",
    "Software",
    "GeneralChunks"
]


class CategorizedRetriever:
    def __init__(self, weaviate_url: str = WEAVIATE_URL):
        self.client = weaviate.Client(weaviate_url)
        self.available_classes = self._get_available_classes()
        
    def _get_available_classes(self) -> List[str]:
        """Get list of available Weaviate classes."""
        try:
            schema = self.client.schema.get()
            return [cls["class"] for cls in schema.get("classes", [])]
        except Exception as e:
            print(f"❌ Error getting Weaviate classes: {e}")
            return []
            
    def determine_target_classes(self, ticket: Optional[SupportTicket] = None, 
                               product_context: Optional[Dict] = None) -> List[str]:
        """
        Determine which Weaviate classes to search based on ticket/product context.
        
        Args:
            ticket: SupportTicket instance with product hierarchy
            product_context: Dict with product information
            
        Returns:
            List of Weaviate class names to search, in priority order
        """
        target_classes = []
        
        # Method 1: Use ticket product hierarchy
        if ticket:
            # Check product subcategory (Area Scan, Line Scan, etc.)
            if ticket.product_subcategory:
                mapped_class = PRODUCT_CATEGORY_MAPPING.get(ticket.product_subcategory)
                if mapped_class and mapped_class in self.available_classes:
                    target_classes.append(mapped_class)
                    
            # Check product type (Camera, Frame Grabber, Software)
            if ticket.product_type:
                mapped_class = PRODUCT_CATEGORY_MAPPING.get(ticket.product_type)
                if mapped_class and mapped_class in self.available_classes:
                    if mapped_class not in target_classes:
                        target_classes.append(mapped_class)
                        
        # Method 2: Use product context
        elif product_context:
            product_type = product_context.get("productType", "")
            product_subcategory = product_context.get("productSubcategory", "")
            
            for category in [product_subcategory, product_type]:
                if category:
                    mapped_class = PRODUCT_CATEGORY_MAPPING.get(category)
                    if mapped_class and mapped_class in self.available_classes:
                        if mapped_class not in target_classes:
                            target_classes.append(mapped_class)
        
        # Method 3: Fallback - search relevant classes in priority order
        if not target_classes:
            target_classes = [cls for cls in CLASS_SEARCH_PRIORITY if cls in self.available_classes]
            
        # Always include GeneralChunks as fallback
        if "GeneralChunks" in self.available_classes and "GeneralChunks" not in target_classes:
            target_classes.append("GeneralChunks")
            
        return target_classes[:3]  # Limit to top 3 classes for performance
        
    def search_categorized_chunks(self, query: str, ticket: Optional[SupportTicket] = None,
                                product_context: Optional[Dict] = None, 
                                limit: int = 5) -> List[Dict]:
        """
        Search for relevant chunks across appropriate Weaviate classes.
        
        Args:
            query: Search query text
            ticket: SupportTicket for context
            product_context: Product information dict
            limit: Maximum number of results to return
            
        Returns:
            List of matching chunks with metadata
        """
        from .views import get_embedding  # Import here to avoid circular imports
        
        try:
            # Get query embedding
            query_embedding = get_embedding(query)
            
            # Determine target classes
            target_classes = self.determine_target_classes(ticket, product_context)
            
            print(f"🎯 Searching classes: {target_classes}")
            
            all_matches = []
            
            # Search each target class
            for class_name in target_classes:
                try:
                    response = (
                        self.client.query
                        .get(class_name, ["source_file", "chunk_number", "content", "category"])
                        .with_near_vector({"vector": query_embedding, "certainty": 0.85})
                        .with_limit(limit)
                        .with_additional(["certainty"])
                        .do()
                    )
                    
                    matches = response.get("data", {}).get("Get", {}).get(class_name, [])
                    
                    # Add class metadata to matches
                    for match in matches:
                        match["weaviate_class"] = class_name
                        match["search_priority"] = target_classes.index(class_name)
                        
                    all_matches.extend(matches)
                    print(f"  📦 {class_name}: {len(matches)} matches")
                    
                except Exception as e:
                    print(f"❌ Error searching class {class_name}: {e}")
                    continue
                    
            # Sort by certainty and priority
            all_matches.sort(key=lambda x: (
                -x.get("_additional", {}).get("certainty", 0),  # Higher certainty first
                x.get("search_priority", 999)  # Lower priority number first
            ))
            
            # Return top results
            return all_matches[:limit]
            
        except Exception as e:
            print(f"❌ Error in categorized search: {e}")
            return []
            
    def get_class_statistics(self) -> Dict:
        """Get statistics about available classes and their content."""
        stats = {}
        
        for class_name in self.available_classes:
            try:
                # Get object count
                result = self.client.query.aggregate(class_name).with_meta_count().do()
                count = result["data"]["Aggregate"][class_name][0]["meta"]["count"]
                
                # Get sample to understand content
                sample = self.client.query.get(class_name, ["source_file"]).with_limit(1).do()
                sample_files = sample.get("data", {}).get("Get", {}).get(class_name, [])
                
                stats[class_name] = {
                    "count": count,
                    "sample_files": [obj.get("source_file") for obj in sample_files]
                }
                
            except Exception as e:
                stats[class_name] = {"count": 0, "error": str(e)}
                
        return stats


# Global retriever instance
_retriever = None

def get_categorized_retriever() -> CategorizedRetriever:
    """Get or create global categorized retriever instance."""
    global _retriever
    if _retriever is None:
        _retriever = CategorizedRetriever()
    return _retriever


def search_similar_chunks_categorized(query: str, ticket: Optional[SupportTicket] = None,
                                    product_context: Optional[Dict] = None, 
                                    limit: int = 5) -> List[Dict]:
    """
    Convenience function for categorized chunk search.
    
    This replaces the old search_similar_chunks_weaviate function with category awareness.
    """
    retriever = get_categorized_retriever()
    return retriever.search_categorized_chunks(query, ticket, product_context, limit)


def get_retrieval_statistics() -> Dict:
    """Get statistics about the categorized retrieval system."""
    retriever = get_categorized_retriever()
    return {
        "available_classes": retriever.available_classes,
        "class_statistics": retriever.get_class_statistics(),
        "category_mapping": PRODUCT_CATEGORY_MAPPING
    }
