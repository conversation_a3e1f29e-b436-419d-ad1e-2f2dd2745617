import uuid
import mysql.connector
from typing import List
import weaviate
import openai
import argparse

# --- Configuration ---
WEAVIATE_URL = "http://localhost:8080"
WEAVIATE_CLASS_NAME = "ChunkEmbeddingsV2"  # Legacy class for backward compatibility
AREA_SCAN_CLASS_NAME = "AreaScanChunks"
LINE_SCAN_CLASS_NAME = "LineScanChunks"
AREASCANV2_CLASS_NAME = "areascanv2"  # New class with model_name field
EMBEDDING_BATCH_SIZE = 1

# OpenAI & Weaviate
openai.api_key = "********************************************************************************************************************************************************************"
client_weaviate = weaviate.Client(WEAVIATE_URL)

# MySQL DB config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough'
}

def get_chunks_from_db() -> List[dict]:
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)
    cursor.execute("SELECT id, source_file, chunk_number, content, camera_type FROM pdf_chunks WHERE vector_embedded = 0")
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    return rows

def update_vectorized_flag(chunk_ids: List[int]):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    format_ids = ",".join(["%s"] * len(chunk_ids))
    cursor.execute(f"UPDATE pdf_chunks SET vector_embedded = 1 WHERE id IN ({format_ids})", chunk_ids)
    conn.commit()
    cursor.close()
    conn.close()

def get_embeddings_batch(texts: List[str]) -> List[List[float]]:
    response = openai.Embedding.create(
        input=texts,
        model="text-embedding-ada-002"
    )
    return [item.embedding for item in response.data]

def setup_weaviate_schema(client: weaviate.Client):
    """
    Checks for and creates necessary Weaviate classes with a case-insensitive check.
    """
    schema = client.schema.get()
    # Get all existing class names and convert them to lowercase for robust comparison
    existing_classes_lower = [c["class"].lower() for c in schema.get("classes", [])]

    # Create Area Scan class
    if AREA_SCAN_CLASS_NAME.lower() not in existing_classes_lower:
        area_scan_class = {
            "class": AREA_SCAN_CLASS_NAME,
            "description": "Stores embeddings for Area Scan camera documents.",
            "properties": [
                {"name": "source_file", "dataType": ["string"]},
                {"name": "chunk_number", "dataType": ["int"]},
                {"name": "content", "dataType": ["text"]},
                {"name": "camera_type", "dataType": ["string"]}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(area_scan_class)
        print(f"Created class '{AREA_SCAN_CLASS_NAME}'.")

    # Create Line Scan class
    if LINE_SCAN_CLASS_NAME.lower() not in existing_classes_lower:
        line_scan_class = {
            "class": LINE_SCAN_CLASS_NAME,
            "description": "Stores embeddings for Line Scan camera documents.",
            "properties": [
                {"name": "source_file", "dataType": ["string"]},
                {"name": "chunk_number", "dataType": ["int"]},
                {"name": "content", "dataType": ["text"]},
                {"name": "camera_type", "dataType": ["string"]}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(line_scan_class)
        print(f"Created class '{LINE_SCAN_CLASS_NAME}'.")

    # Create areascanv2 class with model_name field
    if AREASCANV2_CLASS_NAME.lower() not in existing_classes_lower:
        areascanv2_class = {
            "class": AREASCANV2_CLASS_NAME,
            "description": "Enhanced document chunks with model name filtering support",
            "properties": [
                {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
                {"name": "chunk_number", "dataType": ["int"], "description": "Index of the chunk"},
                {"name": "content", "dataType": ["text"], "description": "Actual chunk text"},
                {"name": "camera_type", "dataType": ["string"], "description": "Camera type (area_scan/line_scan)"},
                {"name": "model_name", "dataType": ["string"], "description": "Model name for filtering (e.g., 'Genie nano 5g')"}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(areascanv2_class)
        print(f"Created class '{AREASCANV2_CLASS_NAME}'.")

    # Keep legacy class for backward compatibility
    if WEAVIATE_CLASS_NAME.lower() not in existing_classes_lower:
        legacy_class = {
            "class": WEAVIATE_CLASS_NAME,
            "description": "Legacy class for backward compatibility.",
            "properties": [
                {"name": "source_file", "dataType": ["string"]},
                {"name": "chunk_number", "dataType": ["int"]},
                {"name": "content", "dataType": ["text"]}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(legacy_class)
        print(f"Created legacy class '{WEAVIATE_CLASS_NAME}'.")

def batch_chunks(chunks: List[dict], batch_size: int):
    for i in range(0, len(chunks), batch_size):
        yield chunks[i:i + batch_size]

def get_weaviate_class_name(camera_type, use_v2=False):
    """Get the appropriate Weaviate class name based on camera type."""
    if use_v2:
        return AREASCANV2_CLASS_NAME  # Use new enhanced class
    elif camera_type == "area_scan":
        return AREA_SCAN_CLASS_NAME
    elif camera_type == "line_scan":
        return LINE_SCAN_CLASS_NAME
    else:
        return WEAVIATE_CLASS_NAME  # Legacy class for backward compatibility

def store_embeddings(chunks: List[dict], use_v2=False, model_name="Genie nano 5g"):
    print(f"Processing {len(chunks)} chunks in batches of {EMBEDDING_BATCH_SIZE}...")
    if use_v2:
        print(f"Using areascanv2 class with model_name: {model_name}")

    for batch in batch_chunks(chunks, EMBEDDING_BATCH_SIZE):
        valid_chunks = [chunk for chunk in batch if chunk.get("content")]
        if not valid_chunks:
            continue

        texts = [chunk["content"] for chunk in valid_chunks]

        try:
            embeddings = get_embeddings_batch(texts)
        except Exception as e:
            print(f"Embedding failed for batch: {e}")
            continue

        successful_ids = []

        for idx in range(len(valid_chunks)):
            chunk = valid_chunks[idx]
            try:
                embedding = embeddings[idx]
                obj_uuid = str(uuid.uuid5(uuid.NAMESPACE_URL, chunk["content"]))

                # Determine the appropriate Weaviate class
                camera_type = chunk.get("camera_type")
                class_name = get_weaviate_class_name(camera_type, use_v2=use_v2)

                # Skip if already exists in Weaviate
                if client_weaviate.data_object.exists(uuid=obj_uuid):
                    print(f"Chunk already exists in Weaviate: {chunk['chunk_number']} from {chunk['source_file']}")
                    continue

                data_object = {
                    "source_file": chunk["source_file"],
                    "chunk_number": int(chunk["chunk_number"]),
                    "content": chunk["content"],
                    "camera_type": camera_type or "unknown"
                }

                # Add model_name field for areascanv2 class
                if use_v2:
                    data_object["model_name"] = model_name

                client_weaviate.data_object.create(
                    data_object=data_object,
                    class_name=class_name,
                    vector=embedding,
                    uuid=obj_uuid
                )

                successful_ids.append(chunk["id"])
                print(f"Vectorized chunk {chunk['chunk_number']} from {chunk['source_file']} to {class_name}")
            except Exception as e:
                print(f"Failed to vectorize chunk: {e}")
                continue

        if successful_ids:
            update_vectorized_flag(successful_ids)

def main(args):
    if args.weaviate:
        setup_weaviate_schema(client_weaviate)
        chunks = get_chunks_from_db()
        if not chunks:
            print("No chunks to vectorize.")
            return

        # Check if --use-v2 flag is provided
        use_v2 = getattr(args, 'use_v2', False)
        model_name = getattr(args, 'model_name', 'Genie nano 5g')

        store_embeddings(chunks, use_v2=use_v2, model_name=model_name)
        print("Vectorization complete.")
    else:
        print("Use --weaviate to start embedding.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--weaviate", action="store_true", help="Use Weaviate for vector storage")
    parser.add_argument("--use-v2", action="store_true", dest="use_v2", help="Use areascanv2 class with model_name field")
    parser.add_argument("--model-name", default="Genie nano 5g", help="Model name for areascanv2 class (default: Genie nano 5g)")
    args = parser.parse_args()
    main(args)
