"""
Category Manager for Future-Proof Architecture

This module provides utilities for managing document categories and Weaviate classes,
making it easy to add new categories and maintain the system.
"""

import weaviate
from typing import Dict, List, Optional, Tuple
from django.conf import settings
from .models import PdfFile


class CategoryManager:
    """Manages document categories and their corresponding Weaviate classes."""
    
    # Core category definitions
    CATEGORY_DEFINITIONS = {
        "AreaScan": {
            "display_name": "Area Scan Cameras",
            "description": "2D matrix cameras for area scanning applications",
            "keywords": ["area", "areascan", "2d", "matrix", "ccd", "cmos", "genie nano", "blackfly"],
            "weaviate_class": "AreaScan",
            "priority": 1
        },
        "LineScan": {
            "display_name": "Line Scan Cameras", 
            "description": "1D linear cameras for line scanning applications",
            "keywords": ["line", "linescan", "1d", "linear", "piranha", "spyder"],
            "weaviate_class": "LineScan",
            "priority": 2
        },
        "FrameGrabber": {
            "display_name": "Frame Grabbers",
            "description": "Image acquisition and frame grabber hardware",
            "keywords": ["frame", "grabber", "acquisition", "capture", "xtium", "xcelera"],
            "weaviate_class": "FrameGrabber", 
            "priority": 3
        },
        "Software": {
            "display_name": "Software & SDKs",
            "description": "Software development kits and programming interfaces",
            "keywords": ["software", "sdk", "api", "driver", "spinnaker", "sapera", "pylon"],
            "weaviate_class": "Software",
            "priority": 4
        },
        "GeneralChunks": {
            "display_name": "General Documentation",
            "description": "General documentation and uncategorized content",
            "keywords": [],
            "weaviate_class": "GeneralChunks",
            "priority": 999
        }
    }
    
    def __init__(self, weaviate_url: str = "http://localhost:8080"):
        self.weaviate_url = weaviate_url
        self.client = weaviate.Client(weaviate_url)
        
    def get_all_categories(self) -> Dict:
        """Get all defined categories."""
        return self.CATEGORY_DEFINITIONS.copy()
        
    def get_category_choices(self) -> List[Tuple[str, str]]:
        """Get category choices for Django model fields."""
        return [
            (cat_id, cat_info["display_name"]) 
            for cat_id, cat_info in self.CATEGORY_DEFINITIONS.items()
        ]
        
    def detect_category(self, filename: str, content: str = "") -> str:
        """
        Detect category based on filename and content.
        
        Args:
            filename: Name of the file
            content: Text content (optional)
            
        Returns:
            Category ID
        """
        text_to_check = f"{filename.lower()} {content.lower()}"
        
        # Sort categories by priority (lower number = higher priority)
        sorted_categories = sorted(
            self.CATEGORY_DEFINITIONS.items(),
            key=lambda x: x[1]["priority"]
        )
        
        for category_id, category_info in sorted_categories:
            keywords = category_info["keywords"]
            if keywords and any(keyword in text_to_check for keyword in keywords):
                return category_id
                
        return "GeneralChunks"  # Default fallback
        
    def get_weaviate_class_for_category(self, category: str) -> str:
        """Get Weaviate class name for a category."""
        return self.CATEGORY_DEFINITIONS.get(category, {}).get("weaviate_class", "GeneralChunks")
        
    def get_search_priority_classes(self, primary_category: Optional[str] = None) -> List[str]:
        """
        Get list of Weaviate classes in search priority order.
        
        Args:
            primary_category: Primary category to search first
            
        Returns:
            List of class names in priority order
        """
        classes = []
        
        # Add primary category first if specified
        if primary_category and primary_category in self.CATEGORY_DEFINITIONS:
            primary_class = self.get_weaviate_class_for_category(primary_category)
            classes.append(primary_class)
            
        # Add other classes in priority order
        sorted_categories = sorted(
            self.CATEGORY_DEFINITIONS.items(),
            key=lambda x: x[1]["priority"]
        )
        
        for category_id, category_info in sorted_categories:
            weaviate_class = category_info["weaviate_class"]
            if weaviate_class not in classes:
                classes.append(weaviate_class)
                
        return classes
        
    def create_weaviate_class(self, category: str) -> bool:
        """
        Create a Weaviate class for a category.
        
        Args:
            category: Category ID
            
        Returns:
            True if successful, False otherwise
        """
        if category not in self.CATEGORY_DEFINITIONS:
            print(f"❌ Unknown category: {category}")
            return False
            
        category_info = self.CATEGORY_DEFINITIONS[category]
        class_name = category_info["weaviate_class"]
        
        try:
            # Check if class already exists
            schema = self.client.schema.get()
            existing_classes = [c["class"] for c in schema.get("classes", [])]
            
            if class_name in existing_classes:
                print(f"✅ Class '{class_name}' already exists")
                return True
                
            # Create class schema
            class_schema = {
                "class": class_name,
                "description": category_info["description"],
                "vectorizer": "none",  # We provide our own vectors
                "properties": [
                    {
                        "name": "source_file",
                        "dataType": ["text"],
                        "description": "Source file name"
                    },
                    {
                        "name": "chunk_number", 
                        "dataType": ["int"],
                        "description": "Chunk number within file"
                    },
                    {
                        "name": "content",
                        "dataType": ["text"],
                        "description": "Text content of chunk"
                    },
                    {
                        "name": "category",
                        "dataType": ["text"],
                        "description": "Document category"
                    },
                    {
                        "name": "migrated_from",
                        "dataType": ["text"],
                        "description": "Original class name (for migrated data)"
                    }
                ]
            }
            
            self.client.schema.create_class(class_schema)
            print(f"✅ Created Weaviate class: {class_name}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating class {class_name}: {e}")
            return False
            
    def create_all_classes(self) -> bool:
        """Create all defined Weaviate classes."""
        success = True
        for category in self.CATEGORY_DEFINITIONS:
            if not self.create_weaviate_class(category):
                success = False
        return success
        
    def get_class_statistics(self) -> Dict:
        """Get statistics for all classes."""
        stats = {}
        
        for category, category_info in self.CATEGORY_DEFINITIONS.items():
            class_name = category_info["weaviate_class"]
            
            try:
                # Get Weaviate object count
                result = self.client.query.aggregate(class_name).with_meta_count().do()
                weaviate_count = result["data"]["Aggregate"][class_name][0]["meta"]["count"]
            except:
                weaviate_count = 0
                
            try:
                # Get Django model count
                django_count = PdfFile.objects.filter(category=category).count()
                processed_count = PdfFile.objects.filter(category=category, is_processed=True).count()
            except:
                django_count = 0
                processed_count = 0
                
            stats[category] = {
                "display_name": category_info["display_name"],
                "weaviate_class": class_name,
                "weaviate_objects": weaviate_count,
                "django_files": django_count,
                "processed_files": processed_count,
                "pending_files": django_count - processed_count
            }
            
        return stats
        
    def add_new_category(self, category_id: str, display_name: str, description: str, 
                        keywords: List[str], priority: int = 100) -> bool:
        """
        Add a new category to the system.
        
        Args:
            category_id: Unique category identifier
            display_name: Human-readable name
            description: Category description
            keywords: List of keywords for detection
            priority: Search priority (lower = higher priority)
            
        Returns:
            True if successful, False otherwise
        """
        if category_id in self.CATEGORY_DEFINITIONS:
            print(f"❌ Category {category_id} already exists")
            return False
            
        # Add to definitions
        self.CATEGORY_DEFINITIONS[category_id] = {
            "display_name": display_name,
            "description": description,
            "keywords": keywords,
            "weaviate_class": category_id,
            "priority": priority
        }
        
        # Create Weaviate class
        success = self.create_weaviate_class(category_id)
        
        if success:
            print(f"✅ Added new category: {category_id}")
            # Note: In production, you'd want to update Django model choices
            # and run migrations to add the new choice to PdfFile.CATEGORY_CHOICES
        
        return success


# Global category manager instance
_category_manager = None

def get_category_manager() -> CategoryManager:
    """Get or create global category manager instance."""
    global _category_manager
    if _category_manager is None:
        _category_manager = CategoryManager()
    return _category_manager
