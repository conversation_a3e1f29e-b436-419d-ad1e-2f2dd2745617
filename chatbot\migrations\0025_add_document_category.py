# Generated migration for adding document category support

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0020_supportticket_product_category_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='pdffile',
            name='category',
            field=models.CharField(
                max_length=50,
                choices=[
                    ('AreaScan', 'Area Scan'),
                    ('LineScan', 'Line Scan'),
                    ('FrameGrabber', 'Frame Grabber'),
                    ('Software', 'Software'),
                    ('GeneralChunks', 'General'),
                ],
                default='GeneralChunks',
                help_text='Document category for Weaviate class selection'
            ),
        ),
        migrations.AddField(
            model_name='pdffile',
            name='is_processed',
            field=models.BooleanField(
                default=False,
                help_text='Whether this file has been chunked and embedded'
            ),
        ),
        migrations.AddField(
            model_name='pdffile',
            name='weaviate_class',
            field=models.Char<PERSON>ield(
                max_length=50,
                blank=True,
                null=True,
                help_text='Weaviate class where this document is stored'
            ),
        ),
        migrations.AddField(
            model_name='pdffile',
            name='chunk_count',
            field=models.IntegerField(
                default=0,
                help_text='Number of chunks created from this document'
            ),
        ),
        migrations.AddField(
            model_name='pdffile',
            name='file_hash',
            field=models.CharField(
                max_length=64,
                blank=True,
                null=True,
                help_text='SHA256 hash of file content for duplicate detection'
            ),
        ),
    ]
