"""
Smart Upload System with Category Detection and Duplicate Prevention

This module handles file uploads with automatic category detection and
prevents re-processing of files that are already in the migrated dataset.
"""

import hashlib
import os
from datetime import datetime
from typing import Optional, Dict, Tuple
from django.core.files.uploadedfile import UploadedFile
from .models import PdfFile
from .categorized_retrieval import get_categorized_retriever


def calculate_file_hash(file_data: bytes) -> str:
    """Calculate SHA256 hash of file content."""
    return hashlib.sha256(file_data).hexdigest()


def detect_category_from_content_and_filename(filename: str, content_sample: str = "") -> str:
    """
    Detect document category based on filename and content.
    
    Args:
        filename: Name of the uploaded file
        content_sample: Sample text content from the file (optional)
        
    Returns:
        Category string matching PdfFile.CATEGORY_CHOICES
    """
    filename_lower = filename.lower()
    content_lower = content_sample.lower()
    
    # Area Scan patterns
    area_patterns = [
        'area', 'areascan', 'area-scan', 'area_scan',
        '2d', 'matrix', 'ccd', 'cmos',
        'genie nano', 'genie micro', 'blackfly'
    ]
    
    # Line Scan patterns  
    line_patterns = [
        'line', 'linescan', 'line-scan', 'line_scan',
        '1d', 'linear', 'piranha', 'spyder'
    ]
    
    # Frame Grabber patterns
    grabber_patterns = [
        'frame', 'grabber', 'framegrabber', 'frame-grabber',
        'acquisition', 'capture', 'xtium', 'xcelera'
    ]
    
    # Software patterns
    software_patterns = [
        'software', 'sdk', 'api', 'driver', 'library',
        'spinnaker', 'sapera', 'pylon', 'vimba',
        'programming', 'development', 'code'
    ]
    
    # Check patterns in both filename and content
    text_to_check = f"{filename_lower} {content_lower}"
    
    if any(pattern in text_to_check for pattern in area_patterns):
        return 'AreaScan'
    elif any(pattern in text_to_check for pattern in line_patterns):
        return 'LineScan'
    elif any(pattern in text_to_check for pattern in grabber_patterns):
        return 'FrameGrabber'
    elif any(pattern in text_to_check for pattern in software_patterns):
        return 'Software'
    else:
        return 'GeneralChunks'


def check_if_file_exists_in_weaviate(filename: str, file_hash: str) -> Tuple[bool, Optional[str]]:
    """
    Check if file already exists in any Weaviate class.
    
    Args:
        filename: Name of the file
        file_hash: SHA256 hash of file content
        
    Returns:
        Tuple of (exists, weaviate_class_name)
    """
    try:
        retriever = get_categorized_retriever()
        
        # Search across all available classes for this filename
        for class_name in retriever.available_classes:
            try:
                # Search by filename
                response = (
                    retriever.client.query
                    .get(class_name, ["source_file", "content"])
                    .with_where({
                        "path": ["source_file"],
                        "operator": "Equal",
                        "valueText": filename
                    })
                    .with_limit(1)
                    .do()
                )
                
                matches = response.get("data", {}).get("Get", {}).get(class_name, [])
                if matches:
                    print(f"✅ File {filename} found in Weaviate class: {class_name}")
                    return True, class_name
                    
            except Exception as e:
                print(f"⚠️ Error checking class {class_name}: {e}")
                continue
                
        return False, None
        
    except Exception as e:
        print(f"❌ Error checking Weaviate: {e}")
        return False, None


def smart_file_upload(uploaded_file: UploadedFile, user_id: Optional[int] = None, override_category: Optional[str] = None) -> Dict:
    """
    Smart file upload with category detection and duplicate prevention.
    
    Args:
        uploaded_file: Django UploadedFile instance
        user_id: ID of uploading user (optional)
        
    Returns:
        Dict with upload result information
    """
    filename = uploaded_file.name
    file_data = uploaded_file.read()
    file_hash = calculate_file_hash(file_data)
    
    result = {
        "filename": filename,
        "file_hash": file_hash,
        "status": "unknown",
        "message": "",
        "category": None,
        "weaviate_class": None,
        "needs_processing": False
    }
    
    try:
        # Step 1: Check if file already exists in database
        existing_file = PdfFile.objects.filter(file_name=filename).first()
        if existing_file:
            if existing_file.file_hash == file_hash:
                result.update({
                    "status": "duplicate",
                    "message": f"File {filename} already exists with same content",
                    "category": existing_file.category,
                    "weaviate_class": existing_file.weaviate_class,
                    "needs_processing": False
                })
                return result
            else:
                # Same filename, different content - update existing record
                print(f"📝 Updating existing file {filename} with new content")
                
        # Step 2: Check if file exists in Weaviate (from migration)
        exists_in_weaviate, weaviate_class = check_if_file_exists_in_weaviate(filename, file_hash)
        
        # Step 3: Detect category (use override if provided)
        if override_category and override_category in [choice[0] for choice in PdfFile.CATEGORY_CHOICES]:
            category = override_category
            print(f"📝 Using user-selected category: {category}")
        else:
            # Auto-detect category from filename
            category = detect_category_from_content_and_filename(filename)
            print(f"🤖 Auto-detected category: {category}")
        
        # Step 4: Create or update database record
        if existing_file:
            # Update existing record
            existing_file.file_data = file_data
            existing_file.file_hash = file_hash
            existing_file.category = category
            existing_file.last_modified = datetime.now()
            
            if exists_in_weaviate:
                existing_file.is_processed = True
                existing_file.weaviate_class = weaviate_class
            else:
                existing_file.is_processed = False
                existing_file.weaviate_class = None
                
            existing_file.save()
            pdf_file = existing_file
        else:
            # Create new record
            pdf_file = PdfFile.objects.create(
                file_name=filename,
                file_data=file_data,
                file_hash=file_hash,
                category=category,
                last_modified=datetime.now(),
                is_processed=exists_in_weaviate,
                weaviate_class=weaviate_class if exists_in_weaviate else None
            )
        
        # Step 5: Determine if processing is needed
        if exists_in_weaviate:
            result.update({
                "status": "migrated",
                "message": f"File {filename} found in migrated data, no processing needed",
                "category": category,
                "weaviate_class": weaviate_class,
                "needs_processing": False
            })
        else:
            result.update({
                "status": "new",
                "message": f"New file {filename} uploaded, processing required",
                "category": category,
                "weaviate_class": None,
                "needs_processing": True
            })
            
        return result
        
    except Exception as e:
        result.update({
            "status": "error",
            "message": f"Error processing file {filename}: {str(e)}",
            "needs_processing": False
        })
        return result


def process_new_file(pdf_file: PdfFile) -> bool:
    """
    Process a new file that needs chunking and embedding.
    
    Args:
        pdf_file: PdfFile instance to process
        
    Returns:
        True if processing successful, False otherwise
    """
    try:
        from .chunking import process_pdf_file_to_weaviate  # Import here to avoid circular imports
        
        print(f"🔄 Processing new file: {pdf_file.file_name}")
        
        # Process the file and store in appropriate Weaviate class
        success = process_pdf_file_to_weaviate(
            pdf_file=pdf_file,
            target_class=pdf_file.category
        )
        
        if success:
            pdf_file.is_processed = True
            pdf_file.weaviate_class = pdf_file.category
            pdf_file.save()
            print(f"✅ Successfully processed {pdf_file.file_name}")
            return True
        else:
            print(f"❌ Failed to process {pdf_file.file_name}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing file {pdf_file.file_name}: {e}")
        return False


def get_upload_statistics() -> Dict:
    """Get statistics about uploaded files and their processing status."""
    try:
        total_files = PdfFile.objects.count()
        processed_files = PdfFile.objects.filter(is_processed=True).count()
        
        # Count by category
        category_stats = {}
        for category, _ in PdfFile.CATEGORY_CHOICES:
            count = PdfFile.objects.filter(category=category).count()
            processed_count = PdfFile.objects.filter(category=category, is_processed=True).count()
            category_stats[category] = {
                "total": count,
                "processed": processed_count,
                "pending": count - processed_count
            }
            
        return {
            "total_files": total_files,
            "processed_files": processed_files,
            "pending_files": total_files - processed_files,
            "category_breakdown": category_stats
        }
        
    except Exception as e:
        return {"error": str(e)}
