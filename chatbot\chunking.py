import mysql.connector
import hashlib
import fitz  # PyMuPDF
import spacy
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Load spaCy model once
nlp = spacy.load("en_core_web_sm")

# MySQL connection setup
conn = mysql.connector.connect(
    host="localhost",
    user="root",
    password="phoobesh333",
    database="rough"
)
cursor = conn.cursor()

# ---------- MySQL Helpers ----------

def fetch_pdf_files_from_db():
    """Fetch PDF files stored as BLOBs from MySQL."""
    cursor.execute("SELECT id, file_name, file_data, camera_type FROM pdf_files")
    return cursor.fetchall()

def fetch_existing_hashes():
    """Fetch file_hashes already processed and stored in the database."""
    cursor.execute("SELECT DISTINCT file_hash FROM pdf_chunks")
    return set(row[0] for row in cursor.fetchall())

def insert_chunks_to_db(chunks):
    """Insert new PDF chunks into the MySQL database."""
    for chunk in chunks:
        cursor.execute("""
            INSERT INTO pdf_chunks
            (source_file, chunk_number, content, file_hash, last_modified, chunked, vector_embedded, camera_type)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            chunk['source_file'],
            int(chunk['chunk_number']),
            chunk['content'],
            chunk['file_hash'],
            chunk['last_modified'],
            chunk['status']['chunked'],
            chunk['status']['vector_embedded'],
            chunk.get('camera_type')
        ))
    conn.commit()

# ---------- Utility Functions ----------

def calculate_sha256_from_bytes(data):
    """Calculate SHA-256 from byte content."""
    return hashlib.sha256(data).hexdigest()

def load_pdf_from_bytes(pdf_bytes):
    """Load and extract text from PDF byte content."""
    try:
        pdf_document = fitz.open("pdf", pdf_bytes)
        text = ""
        for page in pdf_document:
            text += page.get_text()
        pdf_document.close()
        return text
    except Exception as e:
        print(f"Error loading PDF from bytes: {e}")
        return None

def split_into_paragraphs_with_spacy(text):
    """Split text into logical paragraphs using spaCy sentences."""
    doc = nlp(text)
    paragraphs = []
    current_paragraph = []

    for sent in doc.sents:
        current_paragraph.append(sent.text.strip())
        if sent.text.endswith("\n\n") or len(current_paragraph) >= 5:
            paragraphs.append(" ".join(current_paragraph))
            current_paragraph = []

    if current_paragraph:
        paragraphs.append(" ".join(current_paragraph))

    return paragraphs

def dynamic_chunking(paragraphs, max_chunk_size=1000):
    """Combine paragraphs into chunks <= max_chunk_size."""
    chunks = []
    current_chunk = ""
    for paragraph in paragraphs:
        if len(current_chunk) + len(paragraph) + 1 <= max_chunk_size:
            current_chunk += (" " + paragraph) if current_chunk else paragraph
        else:
            chunks.append(current_chunk)
            current_chunk = paragraph
    if current_chunk:
        chunks.append(current_chunk)
    return chunks

# ---------- Main PDF Chunking Logic ----------

def process_all_pdfs_from_db():
    pdf_records = fetch_pdf_files_from_db()
    if not pdf_records:
        print("No PDF records found in DB.")
        return

    existing_hashes = fetch_existing_hashes()
    new_chunks = []

    def process_record(record):
        id_, file_name, file_data, camera_type = record
        file_hash = calculate_sha256_from_bytes(file_data)
        last_modified_str = datetime.now().isoformat()

        if file_hash in existing_hashes:
            print(f"Skipping {file_name} (no content changes).")
            return []

        print(f"Processing {file_name} (Camera Type: {camera_type})...")
        text = load_pdf_from_bytes(file_data)
        if not text:
            return []

        paragraphs = split_into_paragraphs_with_spacy(text)
        chunks_text = dynamic_chunking(paragraphs)

        return [{
            "source_file": file_name,
            "chunk_number": f"{idx+1}",
            "content": chunk,
            "file_hash": file_hash,
            "last_modified": last_modified_str,
            "camera_type": camera_type,
            "status": {
                "chunked": True,
                "vector_embedded": False
            }
        } for idx, chunk in enumerate(chunks_text)]

    with ThreadPoolExecutor() as executor:
        results = executor.map(process_record, pdf_records)
        for chunks in results:
            new_chunks.extend(chunks)

    insert_chunks_to_db(new_chunks)
    print(f"Inserted {len(new_chunks)} new chunk(s).")

# ---------- Main Entry Point ----------

if __name__ == "__main__":
    process_all_pdfs_from_db()